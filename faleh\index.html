<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة مكتب المحاماة - المحامي فالح بن عقاب آل عيسى</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .logo-section {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            border-radius: 20px 20px 0 0;
        }
        
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .btn-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="main-card">
                        <!-- Header Section -->
                        <div class="logo-section text-center p-5">
                            <div class="mb-4">
                                <i class="fas fa-balance-scale fa-4x pulse"></i>
                            </div>
                            <h1 class="display-4 mb-3">🏛️ نظام إدارة مكتب المحاماة</h1>
                            <h3 class="mb-0">المحامي فالح بن عقاب آل عيسى</h3>
                        </div>
                        
                        <!-- Content Section -->
                        <div class="p-5">
                            <div class="text-center mb-5">
                                <h4 class="text-primary mb-3">نظام شامل ومتكامل لإدارة مكاتب المحاماة</h4>
                                <p class="lead text-muted">
                                    حلول تقنية متقدمة لإدارة العملاء والقضايا والمستندات والفواتير
                                </p>
                            </div>
                            
                            <!-- Features Grid -->
                            <div class="row g-4 mb-5">
                                <div class="col-md-3">
                                    <div class="feature-card card h-100 text-center p-3">
                                        <div class="card-body">
                                            <i class="fas fa-users fa-2x text-primary mb-3"></i>
                                            <h6>إدارة العملاء</h6>
                                            <small class="text-muted">إضافة وتعديل بيانات العملاء</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="feature-card card h-100 text-center p-3">
                                        <div class="card-body">
                                            <i class="fas fa-folder-open fa-2x text-success mb-3"></i>
                                            <h6>إدارة القضايا</h6>
                                            <small class="text-muted">متابعة القضايا والجلسات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="feature-card card h-100 text-center p-3">
                                        <div class="card-body">
                                            <i class="fas fa-file-invoice fa-2x text-warning mb-3"></i>
                                            <h6>الفواتير</h6>
                                            <small class="text-muted">إنشاء وطباعة الفواتير</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="feature-card card h-100 text-center p-3">
                                        <div class="card-body">
                                            <i class="fas fa-file-alt fa-2x text-info mb-3"></i>
                                            <h6>المستندات</h6>
                                            <small class="text-muted">رفع وإدارة المستندات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="text-center">
                                <div class="alert alert-warning mb-4">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>تنبيه:</strong> هذا النظام يتطلب خادم Python Flask للعمل
                                </div>
                                
                                <div class="d-grid gap-2 d-md-block">
                                    <button onclick="tryPythonApp()" class="btn btn-custom btn-lg me-2">
                                        <i class="fas fa-rocket me-2"></i>تشغيل النظام
                                    </button>
                                    <button onclick="showInstructions()" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-question-circle me-2"></i>تعليمات التشغيل
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Instructions (Hidden by default) -->
                            <div id="instructions" class="mt-4" style="display: none;">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>تعليمات التشغيل</h5>
                                    </div>
                                    <div class="card-body">
                                        <ol class="mb-0">
                                            <li class="mb-2">تأكد من تثبيت Python على الخادم</li>
                                            <li class="mb-2">ارفع ملف <code>final_working.py</code> و <code>app_for_hosting.py</code></li>
                                            <li class="mb-2">ارفع مجلد <code>instance/</code> و <code>uploads/</code></li>
                                            <li class="mb-2">نفذ الأمر: <code>pip install -r requirements.txt</code></li>
                                            <li class="mb-0">شغل النظام: <code>python app_for_hosting.py</code></li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Footer -->
                        <div class="text-center p-3 border-top">
                            <small class="text-muted">
                                © 2024 نظام إدارة مكتب المحاماة - جميع الحقوق محفوظة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function tryPythonApp() {
            // محاولة الوصول للتطبيق Python
            const possibleUrls = [
                'app_for_hosting.py',
                'final_working.py',
                '/app',
                ':8080',
                ':5000'
            ];
            
            // جرب كل رابط
            for (let url of possibleUrls) {
                try {
                    window.open(url, '_blank');
                    break;
                } catch (e) {
                    console.log('Failed to open:', url);
                }
            }
            
            // رسالة للمستخدم
            setTimeout(() => {
                alert('إذا لم يفتح النظام، تأكد من تشغيل خادم Python أولاً');
            }, 2000);
        }
        
        function showInstructions() {
            const instructions = document.getElementById('instructions');
            if (instructions.style.display === 'none') {
                instructions.style.display = 'block';
                instructions.scrollIntoView({ behavior: 'smooth' });
            } else {
                instructions.style.display = 'none';
            }
        }
        
        // محاولة تلقائية للوصول للتطبيق
        setTimeout(() => {
            fetch('app_for_hosting.py')
                .then(response => {
                    if (response.ok) {
                        window.location.href = 'app_for_hosting.py';
                    }
                })
                .catch(e => {
                    console.log('Python app not running');
                });
        }, 3000);
    </script>
</body>
</html>
