{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('documents.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للمستندات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.file.label(class="form-label") }}
                        {{ form.file(class="form-control" + (" is-invalid" if form.file.errors else "")) }}
                        {% if form.file.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.file.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            الملفات المدعومة: PDF, DOC, DOCX, TXT, JPG, PNG, GIF (الحد الأقصى: 16 ميجابايت)
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.document_type.label(class="form-label") }}
                            {{ form.document_type(class="form-select" + (" is-invalid" if form.document_type.errors else "")) }}
                            {% if form.document_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.document_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                {{ form.is_confidential(class="form-check-input" + (" is-invalid" if form.is_confidential.errors else "")) }}
                                {{ form.is_confidential.label(class="form-check-label") }}
                                {% if form.is_confidential.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.is_confidential.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.tags.label(class="form-label") }}
                        {{ form.tags(class="form-control" + (" is-invalid" if form.tags.errors else "")) }}
                        {% if form.tags.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.tags.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ form.tags.description }}</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.case_id.label(class="form-label") }}
                            {{ form.case_id(class="form-select" + (" is-invalid" if form.case_id.errors else "")) }}
                            {% if form.case_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.case_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.client_id.label(class="form-label") }}
                            {{ form.client_id(class="form-select" + (" is-invalid" if form.client_id.errors else "")) }}
                            {% if form.client_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.client_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('documents.index') }}" class="btn btn-secondary">إلغاء</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">إرشادات الرفع</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success me-2"></i>اختر نوع المستند المناسب</li>
                    <li><i class="fas fa-check text-success me-2"></i>أضف وصفاً واضحاً للمستند</li>
                    <li><i class="fas fa-check text-success me-2"></i>استخدم العلامات للبحث السريع</li>
                    <li><i class="fas fa-check text-success me-2"></i>اربط المستند بالقضية أو العميل</li>
                    <li><i class="fas fa-lock text-warning me-2"></i>فعل "مستند سري" للوثائق الحساسة</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">أنواع الملفات المدعومة</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <i class="fas fa-file-pdf fa-2x text-danger"></i>
                        <br><small>PDF</small>
                    </div>
                    <div class="col-6 mb-2">
                        <i class="fas fa-file-word fa-2x text-primary"></i>
                        <br><small>DOC/DOCX</small>
                    </div>
                    <div class="col-6 mb-2">
                        <i class="fas fa-file-alt fa-2x text-secondary"></i>
                        <br><small>TXT</small>
                    </div>
                    <div class="col-6 mb-2">
                        <i class="fas fa-file-image fa-2x text-success"></i>
                        <br><small>صور</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Preview file name when selected
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('file');
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                // You can add file preview logic here
                console.log('Selected file:', fileName);
            }
        });
    }
});
</script>
{% endblock %}
