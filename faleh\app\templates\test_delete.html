<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار حذف المستخدمين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار وظيفة حذف المستخدمين</h3>
                    </div>
                    <div class="card-body">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <h5>معلومات المستخدم الحالي:</h5>
                        {% if current_user.is_authenticated %}
                            <ul class="list-group mb-3">
                                <li class="list-group-item">الاسم: {{ current_user.full_name }}</li>
                                <li class="list-group-item">اسم المستخدم: {{ current_user.username }}</li>
                                <li class="list-group-item">الدور: {{ current_user.role }}</li>
                                <li class="list-group-item">هل هو مدير؟ {{ 'نعم' if current_user.role == 'admin' else 'لا' }}</li>
                            </ul>
                        {% else %}
                            <div class="alert alert-warning">لم تسجل الدخول</div>
                        {% endif %}
                        
                        <h5>اختبار زر الحذف:</h5>
                        <button class="btn btn-danger" onclick="testDeleteFunction()">
                            <i class="fas fa-trash me-2"></i>اختبار حذف مستخدم وهمي
                        </button>
                        
                        <hr>
                        
                        <h5>اختبار CSRF Token:</h5>
                        <div id="csrf-info"></div>
                        
                        <hr>
                        
                        <div class="mt-3">
                            <a href="{{ url_for('auth.users') }}" class="btn btn-primary">
                                <i class="fas fa-users me-2"></i>انتقل إلى قائمة المستخدمين
                            </a>
                            <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                                <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // اختبار CSRF Token
        function checkCSRF() {
            const csrfToken = document.querySelector('meta[name=csrf-token]');
            const csrfInfo = document.getElementById('csrf-info');
            
            if (csrfToken) {
                csrfInfo.innerHTML = `
                    <div class="alert alert-success">
                        <strong>✅ CSRF Token موجود:</strong><br>
                        <code>${csrfToken.getAttribute('content').substring(0, 20)}...</code>
                    </div>
                `;
            } else {
                csrfInfo.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ CSRF Token غير موجود</strong>
                    </div>
                `;
            }
        }
        
        // اختبار دالة الحذف
        function testDeleteFunction() {
            if (confirm('هل تريد اختبار دالة الحذف؟\n\nهذا مجرد اختبار - لن يتم حذف أي مستخدم فعلي.')) {
                console.log('🧪 اختبار دالة الحذف...');
                
                // محاكاة إنشاء النموذج
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/auth/delete_user/999'; // مستخدم وهمي
                
                const csrfToken = document.querySelector('meta[name=csrf-token]');
                if (csrfToken) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrf_token';
                    csrfInput.value = csrfToken.getAttribute('content');
                    form.appendChild(csrfInput);
                    console.log('✅ CSRF token مضاف');
                } else {
                    console.log('❌ CSRF token غير موجود');
                }
                
                console.log('📝 النموذج جاهز:', form);
                console.log('🎯 الهدف:', form.action);
                
                // لا نرسل النموذج فعلياً في الاختبار
                alert('✅ اختبار النموذج نجح! تحقق من console للتفاصيل.');
            }
        }
        
        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkCSRF();
            console.log('🚀 صفحة الاختبار محملة');
        });
    </script>
</body>
</html>
