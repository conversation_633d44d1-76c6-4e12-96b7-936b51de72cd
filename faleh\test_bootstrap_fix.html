<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح Bootstrap</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            color: white;
        }
        .btn-primary { background: #007bff; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار إصلاح مشكلة Bootstrap</h1>
        
        <div class="test-section">
            <h3>الاختبار الأول: مع Bootstrap</h3>
            <p>هذا الاختبار يحاكي وجود Bootstrap</p>
            <button class="btn btn-primary" onclick="testWithBootstrap()">
                اختبار مع Bootstrap
            </button>
        </div>
        
        <div class="test-section">
            <h3>الاختبار الثاني: بدون Bootstrap</h3>
            <p>هذا الاختبار يحاكي عدم وجود Bootstrap (مثل الخادم السحابي)</p>
            <button class="btn btn-warning" onclick="testWithoutBootstrap()">
                اختبار بدون Bootstrap
            </button>
        </div>
        
        <div class="test-section">
            <h3>الاختبار الثالث: معاينة ملف حقيقي</h3>
            <p>اختبار معاينة ملف من التطبيق</p>
            <button class="btn btn-success" onclick="testRealFile()">
                معاينة ملف حقيقي
            </button>
        </div>
    </div>

    <script>
        // محاكاة دالة showSimplePreview من التطبيق
        function showSimplePreview(docId, filename) {
            try {
                // إنشاء overlay
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    z-index: 9999;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                `;
                
                // إنشاء محتوى المعاينة
                const content = document.createElement('div');
                content.style.cssText = `
                    background: white;
                    border-radius: 8px;
                    padding: 20px;
                    max-width: 90%;
                    max-height: 90%;
                    overflow: auto;
                    position: relative;
                    cursor: default;
                    direction: rtl;
                    text-align: center;
                `;
                
                // زر الإغلاق
                const closeBtn = document.createElement('button');
                closeBtn.innerHTML = '×';
                closeBtn.style.cssText = `
                    position: absolute;
                    top: 10px;
                    left: 15px;
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                `;
                
                // محتوى الملف
                const fileContent = document.createElement('div');
                fileContent.style.cssText = `
                    text-align: center;
                    padding: 20px;
                    min-height: 200px;
                `;
                
                fileContent.innerHTML = `
                    <div style="padding: 40px;">
                        <h4>✅ الإصلاح يعمل بنجاح!</h4>
                        <p><strong>معرف المستند:</strong> ${docId}</p>
                        <p><strong>اسم الملف:</strong> ${filename}</p>
                        <p><strong>الحالة:</strong> معاينة بدون Bootstrap</p>
                        <div style="margin-top: 20px;">
                            <button onclick="alert('تم النقر على زر فتح الملف')" 
                                   style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; margin: 10px; cursor: pointer;">
                                فتح الملف
                            </button>
                            <button onclick="alert('تم النقر على زر تحميل الملف')" 
                                   style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; margin: 10px; cursor: pointer;">
                                تحميل الملف
                            </button>
                        </div>
                    </div>
                `;
                
                // تجميع العناصر
                content.appendChild(closeBtn);
                content.appendChild(fileContent);
                overlay.appendChild(content);
                document.body.appendChild(overlay);
                
                // إغلاق عند النقر على الخلفية
                overlay.addEventListener('click', function(e) {
                    if (e.target === overlay) {
                        document.body.removeChild(overlay);
                    }
                });
                
                // إغلاق عند النقر على زر الإغلاق
                closeBtn.addEventListener('click', function() {
                    document.body.removeChild(overlay);
                });
                
                // إغلاق بمفتاح Escape
                const escapeHandler = function(e) {
                    if (e.key === 'Escape') {
                        if (document.body.contains(overlay)) {
                            document.body.removeChild(overlay);
                            document.removeEventListener('keydown', escapeHandler);
                        }
                    }
                };
                document.addEventListener('keydown', escapeHandler);
                
            } catch (error) {
                console.error('خطأ في showSimplePreview:', error);
                alert('خطأ: ' + error.message);
            }
        }

        function testWithBootstrap() {
            // محاكاة وجود Bootstrap
            window.bootstrap = { Modal: function() {} };
            alert('✅ Bootstrap متاح - سيتم استخدام Modal العادي');
        }

        function testWithoutBootstrap() {
            // محاكاة عدم وجود Bootstrap
            delete window.bootstrap;
            console.log('Bootstrap غير متاح، استخدام modal بسيط');
            showSimplePreview(123, 'test_document.pdf');
        }

        function testRealFile() {
            // اختبار مع ملف حقيقي - استخدام معرف المستند
            showSimplePreview(2, 'اقامة_نعمان_قديمه.jpg');
        }
    </script>
</body>
</html>
