{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('clients.edit', id=client.id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>تعديل
            </a>
            <a href="{{ url_for('clients.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة للعملاء
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Client Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">معلومات العميل</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">الاسم الكامل:</th>
                                <td>{{ client.full_name }}</td>
                            </tr>
                            <tr>
                                <th>البريد الإلكتروني:</th>
                                <td>{{ client.email or '-' }}</td>
                            </tr>
                            <tr>
                                <th>رقم الهاتف:</th>
                                <td>{{ client.phone or '-' }}</td>
                            </tr>
                            <tr>
                                <th>رقم الجوال:</th>
                                <td>{{ client.mobile or '-' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">رقم الهوية:</th>
                                <td>{{ client.national_id or '-' }}</td>
                            </tr>
                            <tr>
                                <th>الشركة:</th>
                                <td>{{ client.company or '-' }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الإضافة:</th>
                                <td>{{ client.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <th>آخر تحديث:</th>
                                <td>{{ client.updated_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if client.address %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>العنوان:</h6>
                        <p class="text-muted">{{ client.address }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if client.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>ملاحظات:</h6>
                        <p class="text-muted">{{ client.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Client Cases -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">قضايا العميل</h5>
                <a href="{{ url_for('cases.add') }}?client_id={{ client.id }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة قضية جديدة
                </a>
            </div>
            <div class="card-body">
                {% if cases %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم القضية</th>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>الحالة</th>
                                <th>المحامي</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for case in cases %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('cases.view', id=case.id) }}" class="text-decoration-none">
                                        {{ case.case_number }}
                                    </a>
                                </td>
                                <td>{{ case.title[:30] }}{% if case.title|length > 30 %}...{% endif %}</td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ {'civil': 'مدنية', 'criminal': 'جنائية', 'commercial': 'تجارية', 
                                            'administrative': 'إدارية', 'labor': 'عمالية', 'family': 'أحوال شخصية',
                                            'real_estate': 'عقارية', 'other': 'أخرى'}[case.case_type] }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if case.status == 'active' else 'secondary' if case.status == 'closed' else 'warning' }}">
                                        {{ {'active': 'نشطة', 'closed': 'مغلقة', 'suspended': 'معلقة'}[case.status] }}
                                    </span>
                                </td>
                                <td>{{ case.lawyer.full_name }}</td>
                                <td>{{ case.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('cases.view', id=case.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-briefcase fa-2x text-muted mb-2"></i>
                    <p class="text-muted">لا توجد قضايا لهذا العميل</p>
                    <a href="{{ url_for('cases.add') }}?client_id={{ client.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة قضية جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('cases.add') }}?client_id={{ client.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة قضية جديدة
                    </a>
                    <a href="{{ url_for('appointments.add') }}?client_id={{ client.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar-plus me-2"></i>حجز موعد
                    </a>
                    <a href="{{ url_for('billing.add') }}?client_id={{ client.id }}" class="btn btn-outline-success">
                        <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة
                    </a>
                    <a href="{{ url_for('clients.edit', id=client.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-edit me-2"></i>تعديل البيانات
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Delete Client -->
        <div class="card mt-3">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">منطقة خطر</h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">حذف العميل سيؤدي إلى حذف جميع البيانات المرتبطة به</p>
                <form method="POST" action="{{ url_for('clients.delete', id=client.id) }}" 
                      onsubmit="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                    <button type="submit" class="btn btn-danger btn-sm">
                        <i class="fas fa-trash me-1"></i>حذف العميل
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
