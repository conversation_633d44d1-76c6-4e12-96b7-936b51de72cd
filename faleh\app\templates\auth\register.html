{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إنشاء مستخدم جديد</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.first_name.label(class="form-label") }}
                            {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                            {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.last_name.label(class="form-label") }}
                            {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                            {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.role.label(class="form-label") }}
                            {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                            {% if form.role.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.password2.label(class="form-label") }}
                            {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else "")) }}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.index') }}" class="btn btn-secondary">إلغاء</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
