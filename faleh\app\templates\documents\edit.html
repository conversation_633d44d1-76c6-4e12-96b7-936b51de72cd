{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('documents.view', id=document.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للمستند
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تعديل معلومات المستند</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.document_type.label(class="form-label") }}
                            {{ form.document_type(class="form-select" + (" is-invalid" if form.document_type.errors else "")) }}
                            {% if form.document_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.document_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                {{ form.is_confidential(class="form-check-input" + (" is-invalid" if form.is_confidential.errors else "")) }}
                                {{ form.is_confidential.label(class="form-check-label") }}
                                {% if form.is_confidential.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.is_confidential.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.tags.label(class="form-label") }}
                        {{ form.tags(class="form-control" + (" is-invalid" if form.tags.errors else "")) }}
                        {% if form.tags.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.tags.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ form.tags.description }}</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.case_id.label(class="form-label") }}
                            {{ form.case_id(class="form-select" + (" is-invalid" if form.case_id.errors else "")) }}
                            {% if form.case_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.case_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.client_id.label(class="form-label") }}
                            {{ form.client_id(class="form-select" + (" is-invalid" if form.client_id.errors else "")) }}
                            {% if form.client_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.client_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('documents.view', id=document.id) }}" class="btn btn-secondary">إلغاء</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Document Info -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">معلومات الملف</h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-file-{{ 'pdf' if document.file_type == 'pdf' else 'word' if document.file_type in ['doc', 'docx'] else 'image' if document.file_type in ['jpg', 'jpeg', 'png', 'gif'] else 'alt' }} fa-2x me-3 text-muted"></i>
                    <div>
                        <strong>{{ document.original_filename }}</strong>
                        <br><small class="text-muted">{{ document.file_type.upper() }}</small>
                    </div>
                </div>
                
                <table class="table table-sm table-borderless">
                    <tr>
                        <th>الحجم:</th>
                        <td>
                            {% if document.file_size %}
                                {% if document.file_size < 1024 %}
                                    {{ document.file_size }} B
                                {% elif document.file_size < 1024*1024 %}
                                    {{ "%.1f"|format(document.file_size/1024) }} KB
                                {% else %}
                                    {{ "%.1f"|format(document.file_size/(1024*1024)) }} MB
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>رفع بواسطة:</th>
                        <td>{{ document.uploader.full_name }}</td>
                    </tr>
                    <tr>
                        <th>تاريخ الرفع:</th>
                        <td>{{ document.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('documents.view', id=document.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>عرض المستند
                    </a>
                    <a href="{{ url_for('documents.download', id=document.id) }}" class="btn btn-outline-success">
                        <i class="fas fa-download me-2"></i>تحميل الملف
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Tips -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">نصائح</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-info-circle text-info me-2"></i>يمكنك تغيير نوع المستند</li>
                    <li><i class="fas fa-tag text-warning me-2"></i>استخدم العلامات للبحث السريع</li>
                    <li><i class="fas fa-link text-success me-2"></i>ربط المستند بالقضية يساعد في التنظيم</li>
                    <li><i class="fas fa-lock text-danger me-2"></i>المستندات السرية محمية من العرض العام</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
