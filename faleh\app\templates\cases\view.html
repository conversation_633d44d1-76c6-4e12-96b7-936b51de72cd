{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('cases.edit', id=case.id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>تعديل
            </a>
            <a href="{{ url_for('appointments.add') }}?case_id={{ case.id }}" class="btn btn-success">
                <i class="fas fa-calendar-plus me-1"></i>إضافة موعد
            </a>
        </div>
        <a href="{{ url_for('cases.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقضايا
        </a>
    </div>
</div>

<div class="row">
    <!-- Case Details -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل القضية</h5>
                <span class="badge bg-{{ 'success' if case.status == 'active' else 'secondary' if case.status == 'closed' else 'warning' }} fs-6">
                    {{ {'active': 'نشطة', 'closed': 'مغلقة', 'suspended': 'معلقة'}[case.status] }}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">رقم القضية:</th>
                                <td><strong>{{ case.case_number }}</strong></td>
                            </tr>
                            <tr>
                                <th>العنوان:</th>
                                <td>{{ case.title }}</td>
                            </tr>
                            <tr>
                                <th>نوع القضية:</th>
                                <td>
                                    <span class="badge bg-info">
                                        {{ {'civil': 'مدنية', 'criminal': 'جنائية', 'commercial': 'تجارية', 
                                            'labor': 'عمالية', 'family': 'أحوال شخصية', 'administrative': 'إدارية',
                                            'real_estate': 'عقارية', 'intellectual_property': 'ملكية فكرية', 'other': 'أخرى'}[case.case_type] }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>العميل:</th>
                                <td>
                                    <a href="{{ url_for('clients.view', id=case.client.id) }}" class="text-decoration-none">
                                        {{ case.client.full_name }}
                                    </a>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">المحامي المسؤول:</th>
                                <td>{{ case.lawyer.full_name }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ البداية:</th>
                                <td>{{ case.start_date }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الانتهاء:</th>
                                <td>{{ case.end_date or 'لم تنته بعد' }}</td>
                            </tr>
                            <tr>
                                <th>موعد الجلسة القادمة:</th>
                                <td>
                                    {% if case.next_hearing %}
                                        <span class="text-primary">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            {{ case.next_hearing.strftime('%Y-%m-%d %H:%M') }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">لا يوجد موعد محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if case.description %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>وصف القضية:</h6>
                        <p class="text-muted">{{ case.description }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Additional Case Information -->
        {% if case.court_name or case.judge_name or case.opposing_party %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">معلومات إضافية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% if case.court_name %}
                            <tr>
                                <th width="40%">المحكمة:</th>
                                <td>{{ case.court_name }}</td>
                            </tr>
                            {% endif %}
                            {% if case.judge_name %}
                            <tr>
                                <th>القاضي:</th>
                                <td>{{ case.judge_name }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% if case.opposing_party %}
                            <tr>
                                <th width="40%">الطرف المقابل:</th>
                                <td>{{ case.opposing_party }}</td>
                            </tr>
                            {% endif %}
                            {% if case.opposing_lawyer %}
                            <tr>
                                <th>محامي الطرف المقابل:</th>
                                <td>{{ case.opposing_lawyer }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Case Timeline -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الجدول الزمني للقضية</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">إنشاء القضية</h6>
                            <p class="timeline-description">تم إنشاء القضية في النظام</p>
                            <small class="text-muted">{{ case.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                    
                    {% if case.start_date %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">بداية القضية</h6>
                            <p class="timeline-description">تاريخ بداية القضية الرسمي</p>
                            <small class="text-muted">{{ case.start_date }}</small>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if case.next_hearing %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">الجلسة القادمة</h6>
                            <p class="timeline-description">موعد الجلسة القادمة</p>
                            <small class="text-muted">{{ case.next_hearing.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if case.end_date %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-secondary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">انتهاء القضية</h6>
                            <p class="timeline-description">تاريخ انتهاء القضية</p>
                            <small class="text-muted">{{ case.end_date }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('cases.edit', id=case.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل القضية
                    </a>
                    <a href="{{ url_for('appointments.add') }}?case_id={{ case.id }}" class="btn btn-success">
                        <i class="fas fa-calendar-plus me-2"></i>إضافة موعد
                    </a>
                    <a href="{{ url_for('documents.upload') }}?case_id={{ case.id }}" class="btn btn-info">
                        <i class="fas fa-upload me-2"></i>رفع مستند
                    </a>
                    <a href="{{ url_for('billing.add') }}?case_id={{ case.id }}" class="btn btn-warning">
                        <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Client Information -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">معلومات العميل</h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-sm me-3">
                        <div class="avatar-title bg-primary rounded-circle">
                            {{ case.client.first_name[0] }}{{ case.client.last_name[0] }}
                        </div>
                    </div>
                    <div>
                        <h6 class="mb-0">{{ case.client.full_name }}</h6>
                        <small class="text-muted">عميل</small>
                    </div>
                </div>
                
                {% if case.client.email %}
                <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ case.client.email }}</p>
                {% endif %}
                {% if case.client.phone %}
                <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ case.client.phone }}</p>
                {% endif %}
                {% if case.client.company %}
                <p class="mb-0"><i class="fas fa-building me-2"></i>{{ case.client.company }}</p>
                {% endif %}
                
                <div class="mt-3">
                    <a href="{{ url_for('clients.view', id=case.client.id) }}" class="btn btn-outline-primary btn-sm">
                        عرض ملف العميل
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Case Statistics -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">إحصائيات القضية</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary">{{ appointments_count }}</h4>
                        <small class="text-muted">المواعيد</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-info">{{ documents_count }}</h4>
                        <small class="text-muted">المستندات</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ invoices_count }}</h4>
                        <small class="text-muted">الفواتير</small>
                    </div>
                    <div class="col-6">
                        {% set days_active = (today - case.created_at.date()).days %}
                        <h4 class="text-secondary">{{ days_active }}</h4>
                        <small class="text-muted">يوم نشطة</small>
                    </div>
                </div>
                
                <hr>
                
                <small class="text-muted">
                    <i class="fas fa-calendar-plus me-1"></i>
                    تم إنشاؤها في {{ case.created_at.strftime('%Y-%m-%d') }}
                    <br>
                    <i class="fas fa-edit me-1"></i>
                    آخر تحديث {{ case.updated_at.strftime('%Y-%m-%d') }}
                </small>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-description {
    margin-bottom: 5px;
    color: #6c757d;
}

.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}
</style>
{% endblock %}
