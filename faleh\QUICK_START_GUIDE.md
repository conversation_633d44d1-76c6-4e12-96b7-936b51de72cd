# 🚀 دليل البدء السريع - نظام إدارة المكتب القانوني

## ⚡ تشغيل النظام في 3 خطوات

### 1️⃣ تشغيل الخادم
```bash
cd faleh
python final_working.py
```

### 2️⃣ فتح المتصفح
اذهب إلى: `http://127.0.0.1:3080`

### 3️⃣ تسجيل الدخول
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

---

## 🎯 الميزات الرئيسية

### 👥 إدارة العملاء
- إضافة عملاء جدد
- البحث في العملاء
- رفع مستندات العملاء

### ⚖️ إدارة القضايا
- إنشاء قضايا جديدة
- ربط القضايا بالعملاء
- تتبع حالة القضايا

### 💰 إدارة الفواتير
- إنشاء فواتير احترافية
- نظام الأقساط
- طباعة الفواتير

### 📅 إدارة المواعيد
- جدولة المواعيد
- تنبيهات المواعيد
- عرض التقويم

### 📊 التقارير
- تقارير شاملة
- إحصائيات مرئية
- تصدير PDF

### ⚙️ إعدادات المكتب
- تعديل بيانات المكتب
- رفع الشعار
- إعدادات الطباعة

---

## 🔐 إدارة المستخدمين

### الأدوار المتاحة:
- **مدير** - صلاحيات كاملة
- **محامي** - إدارة القضايا والعملاء
- **سكرتير** - إدارة المواعيد والمستندات

### إضافة مستخدم جديد:
1. اذهب إلى "إدارة المستخدمين"
2. اضغط "إضافة مستخدم"
3. املأ البيانات واختر الدور
4. احفظ التغييرات

---

## 📱 استخدام النظام

### إضافة عميل جديد:
1. اذهب إلى "العملاء"
2. اضغط "إضافة عميل"
3. املأ البيانات الأساسية
4. ارفع المستندات (اختياري)
5. احفظ البيانات

### إنشاء قضية جديدة:
1. اذهب إلى "القضايا"
2. اضغط "إضافة قضية"
3. اختر العميل
4. املأ تفاصيل القضية
5. احفظ القضية

### إنشاء فاتورة:
1. اذهب إلى "الفواتير"
2. اضغط "إضافة فاتورة"
3. اختر العميل والقضية
4. أضف البنود والمبالغ
5. احفظ واطبع الفاتورة

---

## 🎨 التخصيص

### تغيير شعار المكتب:
1. اذهب إلى "إعدادات المكتب"
2. اضغط "تحديث الشعار"
3. اختر ملف الصورة
4. احفظ التغييرات

### تعديل بيانات المكتب:
1. اذهب إلى "إعدادات المكتب"
2. عدل البيانات المطلوبة
3. احفظ التغييرات

---

## 🔍 البحث والتصفية

### البحث في العملاء:
- بالاسم الأول أو الأخير
- برقم الهاتف
- برقم الهوية الوطنية

### البحث في القضايا:
- برقم القضية
- بعنوان القضية
- باسم العميل

---

## 📊 التقارير والإحصائيات

### التقارير المتاحة:
- تقرير العملاء
- تقرير القضايا
- تقرير الفواتير
- تقرير المواعيد
- تقرير المصروفات

### عرض الإحصائيات:
- لوحة التحكم الرئيسية
- إحصائيات مرئية
- مؤشرات الأداء

---

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### الخادم لا يعمل:
```bash
# تأكد من تشغيل الأمر في المجلد الصحيح
cd faleh
python final_working.py
```

#### لا يمكن الوصول للموقع:
- تأكد من الرابط: `http://127.0.0.1:3080`
- تأكد من عمل الخادم
- أعد تشغيل الخادم

#### مشاكل تسجيل الدخول:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`
- تأكد من كتابة البيانات بشكل صحيح

---

## 📞 الدعم

النظام مكتمل ومعتمد نهائياً!
جميع الميزات تعمل بشكل رائع وجميل ✨

**تاريخ الإصدار**: 18 يوليو 2025
**الحالة**: مكتمل ومعتمد 🎉
