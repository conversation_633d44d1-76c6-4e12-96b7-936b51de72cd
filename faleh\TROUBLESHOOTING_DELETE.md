# دليل استكشاف أخطاء وظيفة حذف المستخدمين

## 🚨 المشكلة: التطبيق لا يعمل أصلاً

### الحلول المقترحة:

#### 1. **تشغيل التطبيق باستخدام ملفات التشغيل الجاهزة:**

**Windows (Command Prompt):**
```cmd
start_app.bat
```

**Windows (PowerShell):**
```powershell
.\start_app.ps1
```

**أو تشغيل مباشر:**
```cmd
python app.py
```

#### 2. **التحقق من Python:**
```cmd
python --version
```
يجب أن يكون Python 3.8 أو أحدث

#### 3. **تثبيت المتطلبات يدوياً:**
```cmd
pip install -r requirements.txt
```

#### 4. **إنشاء قاعدة البيانات:**
```cmd
python init_db.py
```

#### 5. **اختبار التطبيق الأساسي:**
```cmd
python basic_app.py
```

---

## 🔧 إذا كان التطبيق يعمل لكن وظيفة الحذف لا تعمل:

### 1. **تحقق من تسجيل الدخول:**
- تأكد من تسجيل الدخول كمدير
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 2. **تحقق من وجود زر الحذف:**
- انتقل إلى: `http://localhost:5000/auth/users`
- ابحث عن زر الحذف الأحمر (🗑️) بجانب كل مستخدم
- الزر يظهر فقط للمدير وليس للمستخدم الحالي

### 3. **اختبار وظيفة الحذف:**
- انتقل إلى: `http://localhost:5000/auth/test_delete`
- اختبر CSRF Token
- اختبر دالة JavaScript

### 4. **تحقق من Console المتصفح:**
- اضغط F12
- انتقل إلى Console
- ابحث عن أخطاء JavaScript

---

## 🛠️ الملفات المُحدثة لوظيفة الحذف:

### 1. **app/auth/routes.py**
```python
@bp.route('/delete_user/<int:user_id>', methods=['POST'])
@login_required
def delete_user(user_id):
    # كود حذف المستخدم
```

### 2. **app/templates/auth/users.html**
```html
<button class="btn btn-outline-danger" 
        onclick="deleteUser({{ user.id }}, '{{ user.full_name }}')"
        title="حذف المستخدم">
    <i class="fas fa-trash"></i>
</button>
```

### 3. **app/__init__.py**
```python
from flask_wtf.csrf import CSRFProtect
csrf = CSRFProtect()
csrf.init_app(app)
```

### 4. **app/templates/base.html**
```html
<meta name="csrf-token" content="{{ csrf_token() }}">
```

---

## 🧪 اختبار الوظيفة:

### اختبار يدوي:
1. تسجيل الدخول كمدير
2. الانتقال إلى قائمة المستخدمين
3. النقر على زر الحذف
4. تأكيد الحذف

### اختبار تقني:
```cmd
python simple_test.py
```

---

## 🔍 رسائل الخطأ الشائعة:

### "ليس لديك صلاحية لحذف المستخدمين"
- **السبب:** المستخدم ليس مديراً
- **الحل:** تسجيل الدخول كمدير

### "لا يمكنك حذف حسابك الخاص"
- **السبب:** المدير يحاول حذف نفسه
- **الحل:** طبيعي - هذا للحماية

### "لا يمكن حذف المستخدم لأنه مرتبط بقضايا موجودة"
- **السبب:** المستخدم له قضايا مرتبطة
- **الحل:** حذف أو نقل القضايا أولاً

### "حدث خطأ أثناء حذف المستخدم"
- **السبب:** خطأ في قاعدة البيانات
- **الحل:** تحقق من سجل الأخطاء

---

## 📞 الدعم الإضافي:

### إذا استمرت المشكلة:
1. تحقق من ملف `law_office.db` موجود
2. تحقق من صلاحيات الملفات
3. أعد تشغيل التطبيق
4. امسح cache المتصفح (Ctrl+Shift+Delete)

### معلومات مفيدة للدعم:
- إصدار Python: `python --version`
- إصدار Flask: `pip show flask`
- نظام التشغيل: Windows
- المتصفح المستخدم
- رسالة الخطأ الكاملة

---

## ✅ التحقق من نجاح الإصلاح:

1. **التطبيق يعمل:** ✅ / ❌
2. **تسجيل الدخول يعمل:** ✅ / ❌
3. **صفحة المستخدمين تظهر:** ✅ / ❌
4. **زر الحذف يظهر:** ✅ / ❌
5. **وظيفة الحذف تعمل:** ✅ / ❌

إذا كانت جميع النقاط ✅ فالوظيفة تعمل بنجاح!
