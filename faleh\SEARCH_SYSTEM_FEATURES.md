# نظام البحث المتقدم - Advanced Search System

## ✅ تم إضافة نظام البحث المتقدم للقضايا والعملاء

### 🎯 الميزات المضافة:

---

## 🔍 البحث في القضايا

### **البحث بمعايير متعددة:**

#### 1. 📱 **البحث برقم هاتف العميل**
- البحث في حقل رقم الهاتف للعميل المرتبط بالقضية
- يعرض جميع القضايا للعملاء الذين يحتوي رقم هاتفهم على النص المدخل

#### 2. 🆔 **البحث برقم هوية العميل**
- البحث في حقل رقم الهوية الوطنية للعميل
- يعرض جميع القضايا للعملاء الذين يحتوي رقم هويتهم على النص المدخل

#### 3. 👤 **البحث باسم العميل**
- البحث في الاسم الأول أو الأخير أو الاسم الكامل
- يدعم البحث الجزئي (جزء من الاسم)
- يعرض جميع القضايا للعملاء المطابقين

#### 4. 📋 **البحث برقم القضية**
- البحث المباشر في رقم القضية
- يدعم البحث الجزئي

#### 5. 📝 **البحث بعنوان القضية**
- البحث في عنوان/موضوع القضية
- يدعم البحث الجزئي

#### 6. 🌐 **البحث الشامل**
- البحث في جميع الحقول المذكورة أعلاه في نفس الوقت
- الخيار الافتراضي للبحث السريع

---

## 🔍 البحث في العملاء

### **البحث بمعايير متعددة:**

#### 1. 👤 **البحث بالاسم**
- البحث في الاسم الأول أو الأخير أو الاسم الكامل
- يدعم البحث الجزئي

#### 2. 📱 **البحث برقم الهاتف**
- البحث في حقل رقم الهاتف
- يدعم البحث الجزئي

#### 3. 🆔 **البحث برقم الهوية**
- البحث في رقم الهوية الوطنية
- يدعم البحث الجزئي

#### 4. 📧 **البحث بالبريد الإلكتروني**
- البحث في عنوان البريد الإلكتروني
- يدعم البحث الجزئي

#### 5. 🌐 **البحث الشامل**
- البحث في جميع الحقول المذكورة أعلاه

---

## 🎨 واجهة المستخدم

### **نموذج البحث المتقدم:**

#### للقضايا:
- **قائمة منسدلة** لاختيار نوع البحث:
  - البحث في جميع الحقول
  - اسم العميل
  - رقم الهاتف
  - رقم الهوية
  - رقم القضية
  - عنوان القضية

#### للعملاء:
- **قائمة منسدلة** لاختيار نوع البحث:
  - البحث في جميع الحقول
  - الاسم
  - رقم الهاتف
  - رقم الهوية
  - البريد الإلكتروني

### **مربع البحث:**
- حقل نص لإدخال كلمة البحث
- placeholder توضيحي
- يحتفظ بالنص المدخل بعد البحث

### **زر البحث:**
- أيقونة بحث مع النص
- تصميم متجاوب

---

## 📊 عرض النتائج

### **معلومات البحث:**
- عرض كلمة البحث المستخدمة
- عرض نوع البحث المحدد
- عدد النتائج المعثور عليها
- زر لمسح البحث والعودة للعرض الكامل

### **حالات عدم وجود نتائج:**
- رسالة واضحة عند عدم العثور على نتائج
- أيقونة توضيحية
- زر لمسح البحث
- زر لإضافة عنصر جديد

### **حالة عدم وجود بيانات:**
- رسالة مختلفة عندما لا توجد بيانات أصلاً
- دعوة لإضافة البيانات الأولى

---

## 🔧 التقنيات المستخدمة

### **Backend (Python/Flask):**
- استخدام SQLAlchemy ORM للاستعلامات
- `db.or_()` للبحث في حقول متعددة
- `contains()` للبحث الجزئي
- `join()` للربط بين الجداول
- `func.concat()` للبحث في الاسم الكامل

### **Frontend:**
- Bootstrap 5 للتصميم المتجاوب
- Font Awesome للأيقونات
- JavaScript للتفاعل (إذا لزم الأمر)
- دعم كامل للغة العربية (RTL)

---

## 📝 أمثلة الاستخدام

### **البحث في القضايا:**

#### مثال 1: البحث برقم الهاتف
```
نوع البحث: رقم الهاتف
كلمة البحث: 0501234567
النتيجة: جميع القضايا للعملاء الذين يحتوي رقم هاتفهم على هذا الرقم
```

#### مثال 2: البحث باسم العميل
```
نوع البحث: اسم العميل
كلمة البحث: أحمد
النتيجة: جميع القضايا للعملاء الذين يحتوي اسمهم على "أحمد"
```

#### مثال 3: البحث برقم الهوية
```
نوع البحث: رقم الهوية
كلمة البحث: 1234567890
النتيجة: جميع القضايا للعملاء الذين يحتوي رقم هويتهم على هذا الرقم
```

### **البحث الشامل:**
```
نوع البحث: البحث في جميع الحقول
كلمة البحث: محمد
النتيجة: جميع القضايا التي تحتوي على "محمد" في:
- اسم العميل
- رقم الهاتف
- رقم الهوية
- رقم القضية
- عنوان القضية
```

---

## ✅ الاختبار

### **تم اختبار:**
- ✅ البحث برقم الهاتف
- ✅ البحث برقم الهوية
- ✅ البحث باسم العميل
- ✅ البحث برقم القضية
- ✅ البحث بعنوان القضية
- ✅ البحث الشامل
- ✅ البحث الجزئي
- ✅ عرض النتائج
- ✅ مسح البحث
- ✅ حالات عدم وجود نتائج

### **حالات الاختبار:**
1. **بحث ناجح**: يعرض النتائج المطابقة
2. **بحث فارغ**: يعرض جميع البيانات
3. **بحث بدون نتائج**: يعرض رسالة مناسبة
4. **بحث جزئي**: يعثر على المطابقات الجزئية
5. **بحث شامل**: يبحث في جميع الحقول

---

## 🚀 كيفية الاستخدام

### **للوصول للبحث:**
1. اذهب لصفحة القضايا: http://127.0.0.1:8080/cases
2. أو صفحة العملاء: http://127.0.0.1:8080/clients

### **للبحث في القضايا:**
1. اختر نوع البحث من القائمة المنسدلة
2. أدخل كلمة البحث (رقم الهاتف، رقم الهوية، الاسم، إلخ)
3. اضغط زر "بحث"
4. ستظهر النتائج المطابقة

### **لمسح البحث:**
- اضغط زر "مسح البحث" في شريط النتائج
- أو اذهب مباشرة لصفحة القضايا/العملاء

---

## 📁 الملفات المحدثة

### **الملفات المعدلة:**
- `final_working.py` - إضافة وظائف البحث المتقدم

### **الوظائف المحدثة:**
- `cases()` - إضافة نظام البحث للقضايا
- `clients()` - إضافة نظام البحث للعملاء

### **الاستعلامات المضافة:**
- البحث بـ `contains()` للبحث الجزئي
- استخدام `db.or_()` للبحث في حقول متعددة
- `func.concat()` للبحث في الاسم الكامل
- `join()` للربط بين جداول القضايا والعملاء

---

**تاريخ الإضافة**: 2025-07-14  
**الحالة**: مكتمل ومُختبر ✅  
**المطور**: Augment Agent

---

## 🎯 النتيجة النهائية

✅ **نظام بحث متقدم وشامل**  
✅ **البحث برقم الهاتف ورقم الهوية واسم العميل**  
✅ **واجهة مستخدم سهلة ومتجاوبة**  
✅ **دعم البحث الجزئي والشامل**  
✅ **عرض نتائج واضح ومنظم**  

النظام الآن يدعم البحث المتقدم في القضايا والعملاء بجميع المعايير المطلوبة!
