# المحامي فالح بن عقاب آل عيسى - محاماة واستشارات قانونية

نظام شامل لإدارة مكتب المحاماة مطور بلغة Python باستخدام Flask مع واجهة Bootstrap متجاوبة.

## المميزات

### ✅ إدارة القضايا والملفات
- إنشاء ومتابعة القضايا
- ربط القضايا بالعملاء والمحامين والمواعيد
- متابعة مراحل القضية (جلسات، مذكرات، أحكام)
- حفظ الوثائق والمرفقات لكل قضية
- أرشفة القضايا المغلقة

### ✅ إدارة العملاء
- سجل مفصل لكل عميل
- معلومات الاتصال الكاملة
- ربط العملاء بالقضايا
- سجل التواصل مع العميل

### ✅ التقويم والمواعيد
- تقويم متكامل للجلسات والاجتماعات
- تنبيهات للمواعيد القادمة
- جدولة المهام وتوزيعها على المحامين

### ✅ الفوترة والمحاسبة
- تسجيل الأتعاب وإدارة الفواتير
- تتبع المدفوعات والمستحقات
- تقارير مالية شاملة
- إنتاج فواتير PDF

### ✅ إدارة المستخدمين والصلاحيات
- أدوار مختلفة (مدير، محامي، سكرتير)
- ضبط الصلاحيات حسب الدور
- سجل النشاط لكل مستخدم

### ✅ إدارة المستندات
- تخزين المستندات القانونية
- تصنيف المستندات وربطها بالقضايا
- البحث في المستندات
- حماية المستندات السرية

### ✅ التقارير والتحليلات
- تقارير القضايا والأداء
- تقارير الإيرادات والمصروفات
- إحصائيات شاملة
- تصدير التقارير

## متطلبات النظام

- Python 3.8 أو أحدث
- Flask 2.3+
- SQLAlchemy
- Bootstrap 5

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd faleh
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv

# على Windows
venv\Scripts\activate

# على Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة
قم بتعديل ملف `.env` وإضافة المعلومات المطلوبة:
```
SECRET_KEY=your-secret-key-here
FLASK_APP=app.py
FLASK_ENV=development
DATABASE_URL=sqlite:///law_office.db

# إعدادات البريد الإلكتروني
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### 5. تهيئة قاعدة البيانات
```bash
python init_db.py
```

### 6. تشغيل التطبيق
```bash
python app.py
```

سيعمل التطبيق على العنوان: `http://localhost:5000`

## المستخدمون الافتراضيون

بعد تشغيل `init_db.py` ستتوفر الحسابات التالية:

| الدور | اسم المستخدم | كلمة المرور |
|-------|--------------|-------------|
| مدير | admin | admin123 |
| محامي | lawyer1 | lawyer123 |
| سكرتير | secretary1 | secretary123 |

## هيكل المشروع

```
faleh/
├── app/
│   ├── __init__.py
│   ├── models.py
│   ├── main/
│   ├── auth/
│   ├── clients/
│   ├── cases/
│   ├── appointments/
│   ├── billing/
│   ├── documents/
│   ├── reports/
│   └── templates/
├── uploads/
├── config.py
├── app.py
├── init_db.py
├── requirements.txt
└── README.md
```

## الاستخدام

### لوحة التحكم
- عرض إحصائيات شاملة
- القضايا الحديثة
- المواعيد القادمة
- ملخص الفوترة

### إدارة العملاء
- إضافة عملاء جدد
- تعديل بيانات العملاء
- البحث في العملاء
- عرض قضايا كل عميل

### إدارة القضايا
- إنشاء قضايا جديدة
- متابعة حالة القضايا
- ربط القضايا بالعملاء والمحامين
- إدارة مواعيد الجلسات

### نظام المواعيد
- تقويم تفاعلي
- جدولة المواعيد
- تنبيهات المواعيد القادمة

### الفوترة
- إنشاء فواتير جديدة
- تتبع حالة الدفع
- إنتاج فواتير PDF
- تقارير مالية

### إدارة المستندات
- رفع المستندات
- تصنيف المستندات
- البحث في المستندات
- حماية المستندات السرية

## التطوير

### إضافة ميزات جديدة
1. أنشئ blueprint جديد في مجلد `app/`
2. أضف النماذج المطلوبة في `models.py`
3. أنشئ القوالب في `templates/`
4. سجل البلوبرينت في `app/__init__.py`

### قاعدة البيانات
لإجراء تغييرات على قاعدة البيانات:
```bash
flask db migrate -m "وصف التغيير"
flask db upgrade
```

## الأمان

- تشفير كلمات المرور
- حماية المستندات السرية
- صلاحيات مختلفة للمستخدمين
- حماية من CSRF

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
