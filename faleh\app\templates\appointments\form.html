{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('appointments.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للمواعيد
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.appointment_type.label(class="form-label") }}
                            {{ form.appointment_type(class="form-select" + (" is-invalid" if form.appointment_type.errors else "")) }}
                            {% if form.appointment_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.appointment_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                            {% if form.status.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.start_time.label(class="form-label") }}
                            {{ form.start_time(class="form-control" + (" is-invalid" if form.start_time.errors else "")) }}
                            {% if form.start_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.start_time.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.end_time.label(class="form-label") }}
                            {{ form.end_time(class="form-control" + (" is-invalid" if form.end_time.errors else "")) }}
                            {% if form.end_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_time.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.location.label(class="form-label") }}
                        {{ form.location(class="form-control" + (" is-invalid" if form.location.errors else "")) }}
                        {% if form.location.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.location.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.case_id.label(class="form-label") }}
                            {{ form.case_id(class="form-select" + (" is-invalid" if form.case_id.errors else "")) }}
                            {% if form.case_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.case_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.client_id.label(class="form-label") }}
                            {{ form.client_id(class="form-select" + (" is-invalid" if form.client_id.errors else "")) }}
                            {% if form.client_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.client_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('appointments.index') }}" class="btn btn-secondary">إلغاء</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">نصائح</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-info-circle text-info me-2"></i>اختر نوع الموعد المناسب</li>
                    <li><i class="fas fa-clock text-warning me-2"></i>تأكد من صحة التوقيت</li>
                    <li><i class="fas fa-map-marker-alt text-danger me-2"></i>حدد المكان بوضوح</li>
                    <li><i class="fas fa-link text-success me-2"></i>ربط الموعد بالقضية يساعد في التنظيم</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Set default datetime values
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    
    if (!startTimeInput.value) {
        const now = new Date();
        now.setMinutes(0, 0, 0); // Round to nearest hour
        startTimeInput.value = now.toISOString().slice(0, 16);
        
        const endTime = new Date(now.getTime() + 60 * 60 * 1000); // Add 1 hour
        endTimeInput.value = endTime.toISOString().slice(0, 16);
    }
});
</script>
{% endblock %}
