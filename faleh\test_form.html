<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>اختبار نموذج إضافة العميل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        .form-section { margin-bottom: 30px; }
        .section-title { 
            background: #f8f9fa; 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 5px; 
            border-left: 4px solid #007bff; 
        }
        .required { color: red; font-weight: bold; }
        .form-control-lg { font-size: 1.1rem; }
        body { padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="alert alert-warning">
            <h4>🔍 اختبار عرض النموذج</h4>
            <p>هذا ملف HTML مباشر لاختبار عرض جميع الحقول بدون Flask</p>
        </div>
        
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h3 class="mb-0">➕ إضافة عميل جديد مع المستندات</h3>
            </div>
            <div class="card-body">
                <form>
                    <!-- البيانات الأساسية -->
                    <div class="form-section">
                        <div class="section-title">
                            <h5 class="mb-0">👤 البيانات الأساسية للعميل</h5>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الأول <span class="required">*</span></label>
                                    <input type="text" class="form-control form-control-lg" name="first_name" required 
                                           placeholder="أدخل الاسم الأول">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم العائلة <span class="required">*</span></label>
                                    <input type="text" class="form-control form-control-lg" name="last_name" required 
                                           placeholder="أدخل اسم العائلة">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">🆔 رقم الهوية الوطنية</label>
                                    <input type="text" class="form-control form-control-lg" name="national_id" 
                                           placeholder="مثال: 1234567890">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">📱 رقم الهاتف</label>
                                    <input type="text" class="form-control form-control-lg" name="phone" 
                                           placeholder="مثال: 0501234567">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">📧 البريد الإلكتروني</label>
                                    <input type="email" class="form-control form-control-lg" name="email" 
                                           placeholder="مثال: <EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">🏠 العنوان</label>
                                    <input type="text" class="form-control form-control-lg" name="address" 
                                           placeholder="العنوان الكامل">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- المستندات -->
                    <div class="form-section">
                        <div class="section-title">
                            <h5 class="mb-0">📄 المستندات والوثائق (اختياري)</h5>
                        </div>
                        
                        <div class="alert alert-info">
                            <strong>💡 تنبيه:</strong> يمكنك إضافة وصف للمستندات هنا. سيتم إنشاء سجلات للمستندات ويمكن إدارتها لاحقاً من صفحة العميل.
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">🆔 الهوية الشخصية</label>
                                    <input type="text" class="form-control form-control-lg" name="identity_desc" 
                                           placeholder="مثال: هوية وطنية رقم 1234567890">
                                    <small class="text-muted">وصف مستند الهوية الشخصية</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">📋 الوكالة</label>
                                    <input type="text" class="form-control form-control-lg" name="power_of_attorney_desc" 
                                           placeholder="مثال: وكالة عامة مؤرخة 2025/01/15">
                                    <small class="text-muted">وصف مستند الوكالة</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">📄 العقد</label>
                                    <input type="text" class="form-control form-control-lg" name="contract_desc" 
                                           placeholder="مثال: عقد استشارة قانونية">
                                    <small class="text-muted">وصف العقد أو الاتفاقية</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">📎 مستندات أخرى</label>
                                    <input type="text" class="form-control form-control-lg" name="other_desc" 
                                           placeholder="مثال: شهادات، تقارير، مراسلات">
                                    <small class="text-muted">أي مستندات إضافية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg me-3">
                            💾 حفظ العميل والمستندات
                        </button>
                        <button type="reset" class="btn btn-secondary btn-lg">
                            🔄 إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-body">
                <h6><strong>📋 الحقول المتوفرة في هذا النموذج:</strong></h6>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">البيانات الأساسية:</h6>
                        <ul class="list-unstyled">
                            <li>✅ الاسم الأول (مطلوب)</li>
                            <li>✅ اسم العائلة (مطلوب)</li>
                            <li>✅ رقم الهوية الوطنية</li>
                            <li>✅ رقم الهاتف</li>
                            <li>✅ البريد الإلكتروني</li>
                            <li>✅ العنوان</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">المستندات:</h6>
                        <ul class="list-unstyled">
                            <li>✅ وصف الهوية الشخصية</li>
                            <li>✅ وصف الوكالة</li>
                            <li>✅ وصف العقد</li>
                            <li>✅ وصف المستندات الأخرى</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3">
                    <strong>✅ إذا كنت ترى جميع هذه الحقول:</strong> فهذا يعني أن المتصفح يدعم عرض النماذج بشكل صحيح والمشكلة في كود Flask.
                </div>
                
                <div class="alert alert-danger mt-3">
                    <strong>❌ إذا كنت لا ترى بعض الحقول:</strong> فهناك مشكلة في المتصفح أو في عرض CSS.
                </div>
            </div>
        </div>
    </div>
</body>
</html>
