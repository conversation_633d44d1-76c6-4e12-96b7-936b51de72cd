# 🏛️ نظام إدارة مكتب المحاماة

## 📋 وصف المشروع
نظام شامل ومتكامل لإدارة مكاتب المحاماة يشمل:
- إدارة العملاء والقضايا
- نظام الفواتير والمدفوعات
- إدارة المستندات والملفات
- تقارير شاملة ولوحة تحكم
- نظام المستخدمين والصلاحيات

## 👨‍💼 المطور
**المحامي فالح بن عقاب آل عيسى**

## 🚀 التقنيات المستخدمة
- **Backend:** Python Flask
- **Database:** SQLite
- **Frontend:** HTML, CSS, Bootstrap 5
- **Authentication:** Flask-Login

## 📦 التثبيت والتشغيل

### متطلبات النظام:
```bash
pip install -r requirements.txt
```

### تشغيل التطبيق:
```bash
python final_working.py
```

### الوصول للنظام:
- **الرابط:** http://localhost:3080
- **المستخدم:** admin
- **كلمة المرور:** admin123

## 🌟 المميزات الرئيسية

### 👥 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- رفع المستندات الشخصية
- ربط العملاء بالقضايا

### 📁 إدارة القضايا
- تتبع حالة القضايا
- جدولة الجلسات
- ربط المستندات بالقضايا

### 💰 نظام الفواتير
- إنشاء فواتير احترافية
- تتبع المدفوعات
- طباعة الفواتير بتصميم جميل

### 📄 إدارة المستندات
- رفع وتنظيم الملفات
- معاينة المستندات
- ربط المستندات بالقضايا والعملاء

### 📊 التقارير والإحصائيات
- تقارير شاملة عن الأداء
- إحصائيات العملاء والقضايا
- تقارير مالية

## 🔒 الأمان
- تشفير كلمات المرور
- نظام صلاحيات متقدم
- حماية الجلسات

## 📱 التصميم
- تصميم متجاوب (Responsive)
- واجهة عربية جميلة
- سهولة الاستخدام

## 🌐 النشر على Render
يمكن نشر النظام مجاناً على Render:

1. ارفع الكود على GitHub
2. اربط مع Render
3. استخدم الإعدادات:
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `python final_working.py`

## 📞 التواصل
للاستفسارات والدعم الفني، يرجى التواصل مع المطور.

---
© 2024 نظام إدارة مكتب المحاماة - جميع الحقوق محفوظة
