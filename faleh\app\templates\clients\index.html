{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">العملاء</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('clients.add') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-1"></i>إضافة عميل جديد
        </a>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" name="search" class="form-control me-2" 
                   placeholder="البحث في العملاء..." value="{{ search }}">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
</div>

<!-- Clients Table -->
<div class="card">
    <div class="card-body">
        {% if clients %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>الشركة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients %}
                    <tr>
                        <td>
                            <a href="{{ url_for('clients.view', id=client.id) }}" class="text-decoration-none">
                                <strong>{{ client.full_name }}</strong>
                            </a>
                        </td>
                        <td>{{ client.email or '-' }}</td>
                        <td>{{ client.phone or client.mobile or '-' }}</td>
                        <td>{{ client.company or '-' }}</td>
                        <td>{{ client.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('clients.view', id=client.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('clients.edit', id=client.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if prev_url or next_url %}
        <nav aria-label="صفحات العملاء">
            <ul class="pagination justify-content-center">
                {% if prev_url %}
                <li class="page-item">
                    <a class="page-link" href="{{ prev_url }}">السابق</a>
                </li>
                {% endif %}
                {% if next_url %}
                <li class="page-item">
                    <a class="page-link" href="{{ next_url }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عملاء</h5>
            <p class="text-muted">ابدأ بإضافة عميل جديد</p>
            <a href="{{ url_for('clients.add') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-1"></i>إضافة عميل جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
