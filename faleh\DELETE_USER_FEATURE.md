# ميزة حذف المستخدمين

## 📋 الوصف
تم إضافة ميزة حذف المستخدمين إلى نظام إدارة مكتب المحاماة مع ضوابط أمان صارمة.

## 🔐 الصلاحيات
- **المدير فقط** يمكنه حذف المستخدمين
- **لا يمكن للمدير حذف نفسه**
- **لا يمكن حذف مستخدم مرتبط بقضايا أو مواعيد**

## 🛡️ ضوابط الأمان

### 1. التحقق من الصلاحيات
```python
if current_user.role != 'admin':
    flash('ليس لديك صلاحية لحذف المستخدمين', 'danger')
    return redirect(url_for('auth.users'))
```

### 2. منع حذف الذات
```python
if user_to_delete.id == current_user.id:
    flash('لا يمكنك حذف حسابك الخاص', 'danger')
    return redirect(url_for('auth.users'))
```

### 3. التحقق من الارتباطات
```python
# التحقق من القضايا المرتبطة
if user_to_delete.cases.count() > 0:
    flash(f'لا يمكن حذف المستخدم {user_to_delete.full_name} لأنه مرتبط بقضايا موجودة', 'danger')
    return redirect(url_for('auth.users'))

# التحقق من المواعيد المرتبطة
if user_to_delete.appointments.count() > 0:
    flash(f'لا يمكن حذف المستخدم {user_to_delete.full_name} لأنه مرتبط بمواعيد موجودة', 'danger')
    return redirect(url_for('auth.users'))
```

## 🎯 كيفية الاستخدام

### 1. الوصول إلى قائمة المستخدمين
- سجل الدخول كمدير
- انتقل إلى **إدارة المستخدمين** من القائمة الجانبية

### 2. حذف مستخدم
- في جدول المستخدمين، ابحث عن المستخدم المراد حذفه
- انقر على زر **الحذف** (أيقونة سلة المهملات الحمراء)
- أكد العملية في نافذة التأكيد

### 3. رسائل التأكيد
- **نجاح الحذف**: "تم حذف المستخدم [اسم المستخدم] بنجاح"
- **رفض الحذف**: رسائل خطأ توضح سبب عدم إمكانية الحذف

## 🔧 التفاصيل التقنية

### الملفات المُحدثة
1. **`app/auth/routes.py`** - إضافة route جديد للحذف
2. **`app/templates/auth/users.html`** - إضافة زر الحذف وJavaScript
3. **`app/templates/base.html`** - إضافة CSRF token
4. **`app/__init__.py`** - إضافة CSRFProtect

### Route الحذف
```python
@bp.route('/delete_user/<int:user_id>', methods=['POST'])
@login_required
def delete_user(user_id):
    # التحقق من الصلاحيات والضوابط
    # حذف المستخدم إذا كان آمناً
```

### JavaScript للتأكيد
```javascript
function deleteUser(userId, userName) {
    if (confirm(`هل أنت متأكد من حذف المستخدم "${userName}"؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
        // إرسال طلب POST مع CSRF token
    }
}
```

## 🧪 الاختبار
تم إنشاء ملف اختبار شامل `test_delete_user.py` يتضمن:
- اختبار حذف مستخدم بنجاح
- اختبار منع الحذف بدون صلاحية
- اختبار منع المدير من حذف نفسه

### تشغيل الاختبارات
```bash
python test_delete_user.py
```

## ⚠️ تحذيرات مهمة
1. **الحذف نهائي** - لا يمكن التراجع عن حذف المستخدم
2. **النسخ الاحتياطي** - تأكد من وجود نسخة احتياطية قبل الحذف
3. **الارتباطات** - تحقق من عدم وجود بيانات مرتبطة بالمستخدم

## 🔄 التحديثات المستقبلية
- إضافة ميزة "إلغاء تفعيل" بدلاً من الحذف النهائي
- سجل العمليات لتتبع عمليات الحذف
- إمكانية نقل البيانات المرتبطة إلى مستخدم آخر قبل الحذف

## 📞 الدعم
في حالة وجود مشاكل أو استفسارات، يرجى مراجعة:
- ملف `TROUBLESHOOTING.md`
- سجل الأخطاء في التطبيق
- اختبار الوظيفة في بيئة التطوير أولاً
