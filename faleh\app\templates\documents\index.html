{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">المستندات</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('documents.upload') }}" class="btn btn-primary">
            <i class="fas fa-upload me-1"></i>رفع مستند جديد
        </a>
    </div>
</div>

<!-- Search and Filter Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <input type="text" name="search" class="form-control" 
                       placeholder="البحث في المستندات..." value="{{ search }}">
            </div>
            <div class="col-md-2">
                <select name="document_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="contract" {{ 'selected' if document_type == 'contract' }}>عقد</option>
                    <option value="evidence" {{ 'selected' if document_type == 'evidence' }}>دليل</option>
                    <option value="correspondence" {{ 'selected' if document_type == 'correspondence' }}>مراسلات</option>
                    <option value="court_document" {{ 'selected' if document_type == 'court_document' }}>مستند محكمة</option>
                    <option value="identification" {{ 'selected' if document_type == 'identification' }}>هوية</option>
                    <option value="financial" {{ 'selected' if document_type == 'financial' }}>مالي</option>
                    <option value="legal_memo" {{ 'selected' if document_type == 'legal_memo' }}>مذكرة قانونية</option>
                    <option value="other" {{ 'selected' if document_type == 'other' }}>أخرى</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="case_id" class="form-select">
                    <option value="0">جميع القضايا</option>
                    <!-- سيتم ملؤها من البيانات -->
                </select>
            </div>
            <div class="col-md-2">
                <select name="client_id" class="form-select">
                    <option value="0">جميع العملاء</option>
                    <!-- سيتم ملؤها من البيانات -->
                </select>
            </div>
            <div class="col-md-2">
                <select name="is_confidential" class="form-select">
                    <option value="">جميع المستندات</option>
                    <option value="1" {{ 'selected' if is_confidential == '1' }}>سرية فقط</option>
                    <option value="0" {{ 'selected' if is_confidential == '0' }}>غير سرية فقط</option>
                </select>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Documents Table -->
<div class="card">
    <div class="card-body">
        {% if documents %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>اسم الملف</th>
                        <th>النوع</th>
                        <th>الحجم</th>
                        <th>القضية</th>
                        <th>العميل</th>
                        <th>السرية</th>
                        <th>تاريخ الرفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for document in documents %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-{{ 'pdf' if document.file_type == 'pdf' else 'word' if document.file_type in ['doc', 'docx'] else 'image' if document.file_type in ['jpg', 'jpeg', 'png', 'gif'] else 'alt' }} me-2 text-muted"></i>
                                <div>
                                    <a href="{{ url_for('documents.view', id=document.id) }}" class="text-decoration-none">
                                        <strong>{{ document.original_filename }}</strong>
                                    </a>
                                    {% if document.description %}
                                    <br><small class="text-muted">{{ document.description[:50] }}{% if document.description|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ {'contract': 'عقد', 'evidence': 'دليل', 'correspondence': 'مراسلات', 
                                    'court_document': 'مستند محكمة', 'identification': 'هوية', 'financial': 'مالي',
                                    'legal_memo': 'مذكرة قانونية', 'other': 'أخرى'}[document.document_type] }}
                            </span>
                        </td>
                        <td>
                            {% if document.file_size %}
                                {% if document.file_size < 1024 %}
                                    {{ document.file_size }} B
                                {% elif document.file_size < 1024*1024 %}
                                    {{ "%.1f"|format(document.file_size/1024) }} KB
                                {% else %}
                                    {{ "%.1f"|format(document.file_size/(1024*1024)) }} MB
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if document.case %}
                                <a href="{{ url_for('cases.view', id=document.case.id) }}" class="text-decoration-none">
                                    {{ document.case.case_number }}
                                </a>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if document.client %}
                                <a href="{{ url_for('clients.view', id=document.client.id) }}" class="text-decoration-none">
                                    {{ document.client.full_name }}
                                </a>
                            {% elif document.case and document.case.client %}
                                <a href="{{ url_for('clients.view', id=document.case.client.id) }}" class="text-decoration-none">
                                    {{ document.case.client.full_name }}
                                </a>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if document.is_confidential %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-lock me-1"></i>سري
                                </span>
                            {% else %}
                                <span class="badge bg-success">
                                    <i class="fas fa-unlock me-1"></i>عام
                                </span>
                            {% endif %}
                        </td>
                        <td>{{ document.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('documents.view', id=document.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('documents.download', id=document.id) }}" 
                                   class="btn btn-outline-success" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </a>
                                {% if current_user.role in ['admin', 'lawyer'] or document.uploaded_by == current_user.id %}
                                <a href="{{ url_for('documents.edit', id=document.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if prev_url or next_url %}
        <nav aria-label="صفحات المستندات">
            <ul class="pagination justify-content-center">
                {% if prev_url %}
                <li class="page-item">
                    <a class="page-link" href="{{ prev_url }}">السابق</a>
                </li>
                {% endif %}
                {% if next_url %}
                <li class="page-item">
                    <a class="page-link" href="{{ next_url }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مستندات</h5>
            <p class="text-muted">ابدأ برفع مستند جديد</p>
            <a href="{{ url_for('documents.upload') }}" class="btn btn-primary">
                <i class="fas fa-upload me-1"></i>رفع مستند جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
