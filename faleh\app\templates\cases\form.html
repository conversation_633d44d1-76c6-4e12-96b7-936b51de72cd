{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('cases.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقضايا
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.case_number.label(class="form-label") }}
                            {{ form.case_number(class="form-control" + (" is-invalid" if form.case_number.errors else "")) }}
                            {% if form.case_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.case_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.case_type.label(class="form-label") }}
                            {{ form.case_type(class="form-select" + (" is-invalid" if form.case_type.errors else "")) }}
                            {% if form.case_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.case_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="4") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.client_id.label(class="form-label") }}
                            {{ form.client_id(class="form-select" + (" is-invalid" if form.client_id.errors else "")) }}
                            {% if form.client_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.client_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.lawyer_id.label(class="form-label") }}
                            {{ form.lawyer_id(class="form-select" + (" is-invalid" if form.lawyer_id.errors else "")) }}
                            {% if form.lawyer_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.lawyer_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                            {% if form.status.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.priority.label(class="form-label") }}
                            {{ form.priority(class="form-select" + (" is-invalid" if form.priority.errors else "")) }}
                            {% if form.priority.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.priority.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.start_date.label(class="form-label") }}
                            {{ form.start_date(class="form-control" + (" is-invalid" if form.start_date.errors else "")) }}
                            {% if form.start_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.start_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.court_name.label(class="form-label") }}
                            {{ form.court_name(class="form-control" + (" is-invalid" if form.court_name.errors else "")) }}
                            {% if form.court_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.court_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.judge_name.label(class="form-label") }}
                            {{ form.judge_name(class="form-control" + (" is-invalid" if form.judge_name.errors else "")) }}
                            {% if form.judge_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.judge_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.opposing_party.label(class="form-label") }}
                            {{ form.opposing_party(class="form-control" + (" is-invalid" if form.opposing_party.errors else "")) }}
                            {% if form.opposing_party.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.opposing_party.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.opposing_lawyer.label(class="form-label") }}
                            {{ form.opposing_lawyer(class="form-control" + (" is-invalid" if form.opposing_lawyer.errors else "")) }}
                            {% if form.opposing_lawyer.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.opposing_lawyer.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.end_date.label(class="form-label") }}
                            {{ form.end_date(class="form-control" + (" is-invalid" if form.end_date.errors else "")) }}
                            {% if form.end_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.next_hearing_date.label(class="form-label") }}
                            {{ form.next_hearing_date(class="form-control" + (" is-invalid" if form.next_hearing_date.errors else "")) }}
                            {% if form.next_hearing_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.next_hearing_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">اختياري - اتركه فارغاً إذا لم يكن هناك موعد محدد</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.next_hearing_time.label(class="form-label") }}
                            {{ form.next_hearing_time(class="form-control" + (" is-invalid" if form.next_hearing_time.errors else "")) }}
                            {% if form.next_hearing_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.next_hearing_time.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">اختياري - مثال: 10:30</div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('cases.index') }}" class="btn btn-secondary">إلغاء</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
