{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">المواعيد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('appointments.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>إضافة موعد جديد
            </a>
            <a href="{{ url_for('appointments.calendar') }}" class="btn btn-outline-primary">
                <i class="fas fa-calendar me-1"></i>عرض التقويم
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <input type="text" name="search" class="form-control" 
                       placeholder="البحث في المواعيد..." value="{{ search }}">
            </div>
            <div class="col-md-3">
                <select name="appointment_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="hearing" {{ 'selected' if appointment_type == 'hearing' }}>جلسة محكمة</option>
                    <option value="meeting" {{ 'selected' if appointment_type == 'meeting' }}>اجتماع</option>
                    <option value="consultation" {{ 'selected' if appointment_type == 'consultation' }}>استشارة</option>
                    <option value="other" {{ 'selected' if appointment_type == 'other' }}>أخرى</option>
                </select>
            </div>
            <div class="col-md-3">
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="scheduled" {{ 'selected' if status == 'scheduled' }}>مجدول</option>
                    <option value="completed" {{ 'selected' if status == 'completed' }}>مكتمل</option>
                    <option value="cancelled" {{ 'selected' if status == 'cancelled' }}>ملغي</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Appointments Table -->
<div class="card">
    <div class="card-body">
        {% if appointments %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>العنوان</th>
                        <th>النوع</th>
                        <th>التاريخ والوقت</th>
                        <th>المكان</th>
                        <th>القضية</th>
                        <th>العميل</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in appointments %}
                    <tr>
                        <td>
                            <a href="{{ url_for('appointments.view', id=appointment.id) }}" class="text-decoration-none">
                                <strong>{{ appointment.title }}</strong>
                            </a>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'danger' if appointment.appointment_type == 'hearing' else 'primary' if appointment.appointment_type == 'meeting' else 'success' if appointment.appointment_type == 'consultation' else 'secondary' }}">
                                {{ {'hearing': 'جلسة محكمة', 'meeting': 'اجتماع', 'consultation': 'استشارة', 'other': 'أخرى'}[appointment.appointment_type] }}
                            </span>
                        </td>
                        <td>
                            <div>
                                <strong>{{ appointment.start_time.strftime('%Y-%m-%d') }}</strong><br>
                                <small class="text-muted">{{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}</small>
                            </div>
                        </td>
                        <td>{{ appointment.location or '-' }}</td>
                        <td>
                            {% if appointment.case %}
                                <a href="{{ url_for('cases.view', id=appointment.case.id) }}" class="text-decoration-none">
                                    {{ appointment.case.case_number }}
                                </a>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if appointment.case and appointment.case.client %}
                                {{ appointment.case.client.full_name }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'warning' if appointment.status == 'scheduled' else 'success' if appointment.status == 'completed' else 'danger' }}">
                                {{ {'scheduled': 'مجدول', 'completed': 'مكتمل', 'cancelled': 'ملغي'}[appointment.status] }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('appointments.view', id=appointment.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('appointments.edit', id=appointment.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if prev_url or next_url %}
        <nav aria-label="صفحات المواعيد">
            <ul class="pagination justify-content-center">
                {% if prev_url %}
                <li class="page-item">
                    <a class="page-link" href="{{ prev_url }}">السابق</a>
                </li>
                {% endif %}
                {% if next_url %}
                <li class="page-item">
                    <a class="page-link" href="{{ next_url }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مواعيد</h5>
            <p class="text-muted">ابدأ بإضافة موعد جديد</p>
            <a href="{{ url_for('appointments.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>إضافة موعد جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
