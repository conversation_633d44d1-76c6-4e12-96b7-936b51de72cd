# دليل البدء السريع - المحامي فالح بن عقاب آل عيسى

## 🚀 التشغيل السريع

### الطريقة الأولى (الأسهل):
```bash
python run.py
```

### الطريقة الثانية (يدوياً):
```bash
# 1. تثبيت المتطلبات
pip install flask flask-sqlalchemy flask-login flask-wtf flask-mail flask-migrate python-dotenv pillow reportlab openpyxl python-dateutil email-validator bcrypt

# 2. اختبار النظام
python test_app.py

# 3. تشغيل التطبيق
python app.py
```

## 🌐 الوصول للنظام

- **الرابط**: http://localhost:5000
- **المستخدم الافتراضي**: admin
- **كلمة المرور**: admin123

## 📋 الوظائف الرئيسية

### 1. لوحة التحكم
- إحصائيات شاملة
- القضايا الحديثة
- المواعيد القادمة
- ملخص الفوترة

### 2. إدارة العملاء
- إضافة عملاء جدد
- تعديل بيانات العملاء
- البحث في العملاء
- عرض قضايا كل عميل

### 3. إدارة القضايا
- إنشاء قضايا جديدة
- متابعة حالة القضايا
- ربط القضايا بالعملاء والمحامين
- تصنيف القضايا حسب النوع

### 4. نظام المواعيد
- تقويم تفاعلي
- جدولة المواعيد
- أنواع مختلفة من المواعيد
- تنبيهات المواعيد القادمة

### 5. نظام الفوترة
- إنشاء فواتير جديدة
- تتبع حالة الدفع
- إنتاج فواتير PDF
- تقارير مالية

### 6. إدارة المستندات
- رفع المستندات
- تصنيف المستندات
- البحث في المستندات
- حماية المستندات السرية

### 7. التقارير
- تقارير القضايا
- تقارير مالية
- تقارير المواعيد
- تصدير البيانات

## 👥 أدوار المستخدمين

### مدير (Admin)
- الوصول الكامل لجميع الوظائف
- إدارة المستخدمين
- عرض جميع البيانات

### محامي (Lawyer)
- إدارة القضايا المخصصة له
- إدارة العملاء
- إدارة المواعيد
- إنشاء الفواتير

### سكرتير (Secretary)
- إدارة المواعيد
- إدارة العملاء
- رفع المستندات
- عرض التقارير الأساسية

## 🔧 إعدادات متقدمة

### تخصيص قاعدة البيانات
قم بتعديل ملف `.env`:
```
DATABASE_URL=sqlite:///law_office.db
# أو للاستخدام مع PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost/law_office
```

### إعدادات البريد الإلكتروني
```
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

## 🛠️ استكشاف الأخطاء

### مشكلة: لا يمكن الوصول للموقع
- تأكد من تشغيل التطبيق
- تحقق من المنفذ 5000
- جرب http://127.0.0.1:5000

### مشكلة: خطأ في قاعدة البيانات
```bash
python test_app.py
```

### مشكلة: خطأ في المتطلبات
```bash
pip install -r requirements.txt
```

## 📞 الدعم الفني

للحصول على المساعدة:
1. تحقق من ملف README.md
2. راجع رسائل الخطأ في Terminal
3. تأكد من تثبيت جميع المتطلبات

## 🔐 الأمان

- غير كلمة مرور المدير الافتراضية
- استخدم HTTPS في الإنتاج
- قم بعمل نسخ احتياطية منتظمة
- حدث المتطلبات بانتظام

## 📱 الاستجابة

النظام متجاوب ويعمل على:
- أجهزة الكمبيوتر
- الأجهزة اللوحية
- الهواتف الذكية

## 🎯 نصائح للاستخدام الأمثل

1. **ابدأ بإضافة العملاء** قبل إنشاء القضايا
2. **استخدم التقويم** لمتابعة المواعيد
3. **ارفع المستندات** وصنفها بوضوح
4. **راجع التقارير** بانتظام لمتابعة الأداء
5. **استخدم البحث** للعثور على البيانات بسرعة

---

**المحامي فالح بن عقاب آل عيسى** - محاماة واستشارات قانونية
