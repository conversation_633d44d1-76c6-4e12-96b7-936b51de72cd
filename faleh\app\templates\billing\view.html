{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('billing.generate_pdf', id=invoice.id) }}" class="btn btn-success" target="_blank">
                <i class="fas fa-file-pdf me-1"></i>تصدير PDF
            </a>
            <a href="{{ url_for('billing.edit', id=invoice.id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>تعديل
            </a>
        </div>
        <a href="{{ url_for('billing.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للفوترة
        </a>
    </div>
</div>

<div class="row">
    <!-- Invoice Details -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل الفاتورة</h5>
                <span class="badge bg-{{ 'warning' if invoice.status == 'pending' else 'success' if invoice.status == 'paid' else 'danger' if invoice.status == 'overdue' else 'secondary' }} fs-6">
                    {{ {'pending': 'معلقة', 'paid': 'مدفوعة', 'overdue': 'متأخرة', 'cancelled': 'ملغية'}[invoice.status] }}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">رقم الفاتورة:</th>
                                <td><strong>{{ invoice.invoice_number }}</strong></td>
                            </tr>
                            <tr>
                                <th>العميل:</th>
                                <td>
                                    <a href="{{ url_for('clients.view', id=invoice.client.id) }}" class="text-decoration-none">
                                        {{ invoice.client.full_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <th>القضية:</th>
                                <td>
                                    {% if invoice.case %}
                                        <a href="{{ url_for('cases.view', id=invoice.case.id) }}" class="text-decoration-none">
                                            {{ invoice.case.case_number }} - {{ invoice.case.title }}
                                        </a>
                                    {% else %}
                                        غير مرتبطة بقضية
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>تاريخ الإصدار:</th>
                                <td>{{ invoice.issue_date }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">تاريخ الاستحقاق:</th>
                                <td>
                                    {{ invoice.due_date }}
                                    {% if invoice.status == 'pending' and invoice.due_date < today %}
                                        <span class="badge bg-danger ms-2">متأخرة</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>تاريخ الدفع:</th>
                                <td>{{ invoice.paid_date or 'لم يتم الدفع بعد' }}</td>
                            </tr>
                            <tr>
                                <th>طريقة الدفع:</th>
                                <td>
                                    {% if invoice.payment_method %}
                                        {{ {'cash': 'نقداً', 'bank_transfer': 'تحويل بنكي', 'check': 'شيك', 'credit_card': 'بطاقة ائتمان'}[invoice.payment_method] }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>تاريخ الإنشاء:</th>
                                <td>{{ invoice.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if invoice.description %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>الوصف:</h6>
                        <p class="text-muted">{{ invoice.description }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if invoice.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>ملاحظات:</h6>
                        <p class="text-muted">{{ invoice.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Invoice Summary -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">ملخص المبالغ</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <th>المبلغ الأساسي:</th>
                                <td class="text-end">{{ "%.2f"|format(invoice.amount) }} ر.س</td>
                            </tr>
                            <tr>
                                <th>الضريبة:</th>
                                <td class="text-end">{{ "%.2f"|format(invoice.tax_amount) }} ر.س</td>
                            </tr>
                            <tr class="border-top">
                                <th class="fs-5">المبلغ الإجمالي:</th>
                                <td class="text-end fs-5"><strong>{{ "%.2f"|format(invoice.total_amount) }} ر.س</strong></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 bg-light rounded">
                            <h3 class="text-primary mb-0">{{ "%.2f"|format(invoice.total_amount) }}</h3>
                            <small class="text-muted">ريال سعودي</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('billing.generate_pdf', id=invoice.id) }}" class="btn btn-success" target="_blank">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </a>
                    <a href="{{ url_for('billing.edit', id=invoice.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل الفاتورة
                    </a>
                    <a href="{{ url_for('billing.add') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>فاتورة جديدة
                    </a>
                    {% if invoice.client %}
                    <a href="{{ url_for('clients.view', id=invoice.client.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-user me-2"></i>عرض العميل
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Payment Status -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">حالة الدفع</h6>
            </div>
            <div class="card-body">
                {% if invoice.status == 'paid' %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم الدفع بتاريخ {{ invoice.paid_date }}
                    </div>
                {% elif invoice.status == 'overdue' %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        الفاتورة متأخرة عن موعد الاستحقاق
                    </div>
                {% elif invoice.status == 'pending' %}
                    <div class="alert alert-warning">
                        <i class="fas fa-clock me-2"></i>
                        في انتظار الدفع
                        {% if invoice.due_date %}
                            <br><small>تستحق في {{ invoice.due_date }}</small>
                        {% endif %}
                    </div>
                {% else %}
                    <div class="alert alert-secondary">
                        <i class="fas fa-ban me-2"></i>
                        تم إلغاء الفاتورة
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Client Information -->
        {% if invoice.client %}
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">معلومات العميل</h6>
            </div>
            <div class="card-body">
                <h6>{{ invoice.client.full_name }}</h6>
                {% if invoice.client.email %}
                <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ invoice.client.email }}</p>
                {% endif %}
                {% if invoice.client.phone %}
                <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ invoice.client.phone }}</p>
                {% endif %}
                {% if invoice.client.company %}
                <p class="mb-0"><i class="fas fa-building me-2"></i>{{ invoice.client.company }}</p>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Invoice Statistics -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">إحصائيات</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <i class="fas fa-calendar me-1"></i>
                    تم إنشاء الفاتورة في {{ invoice.created_at.strftime('%Y-%m-%d %H:%M') }}
                    <br><br>
                    <i class="fas fa-info-circle me-1"></i>
                    رقم الفاتورة: {{ invoice.invoice_number }}
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
