{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل الملف الشخصي
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">معلومات المستخدم</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">الاسم الكامل:</th>
                                <td>{{ user.full_name }}</td>
                            </tr>
                            <tr>
                                <th>اسم المستخدم:</th>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <th>البريد الإلكتروني:</th>
                                <td>{{ user.email }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">رقم الهاتف:</th>
                                <td>{{ user.phone or '-' }}</td>
                            </tr>
                            <tr>
                                <th>الدور:</th>
                                <td>
                                    <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'primary' if user.role == 'lawyer' else 'secondary' }}">
                                        {{ {'admin': 'مدير', 'lawyer': 'محامي', 'secretary': 'سكرتير'}[user.role] }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>الحالة:</th>
                                <td>
                                    <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                                        {{ 'نشط' if user.is_active else 'غير نشط' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>تاريخ الإنشاء:</h6>
                        <p class="text-muted">{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>آخر زيارة:</h6>
                        <p class="text-muted">{{ user.last_seen.strftime('%Y-%m-%d %H:%M') if user.last_seen else 'لم يسجل دخول من قبل' }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        {% if user.role == 'lawyer' %}
        <!-- Lawyer Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">إحصائيات المحامي</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <h3 class="text-primary">{{ user.cases.count() }}</h3>
                        <p class="text-muted mb-0">إجمالي القضايا</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="text-success">{{ user.cases.filter_by(status='active').count() }}</h3>
                        <p class="text-muted mb-0">القضايا النشطة</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="text-info">{{ user.appointments.count() }}</h3>
                        <p class="text-muted mb-0">إجمالي المواعيد</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل الملف الشخصي
                    </a>
                    <a href="#" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                    </a>
                    {% if user.role == 'lawyer' %}
                    <a href="{{ url_for('cases.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-briefcase me-2"></i>قضاياي
                    </a>
                    <a href="{{ url_for('appointments.index') }}" class="btn btn-outline-info">
                        <i class="fas fa-calendar me-2"></i>مواعيدي
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">معلومات النظام</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    المحامي فالح بن عقاب آل عيسى<br>
                    محاماة واستشارات قانونية<br>
                    الإصدار 1.0<br>
                    تم التطوير باستخدام Flask & Bootstrap
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>لتغيير كلمة المرور، يرجى الانتقال إلى صفحة تعديل الملف الشخصي.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary">تعديل الملف الشخصي</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
