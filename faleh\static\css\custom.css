/* نظام إدارة المكتب القانوني - التصميم المخصص */
/* Law Office Management System - Custom Styles */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --gold-color: #d4af37;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* الخط العربي المحسن */
body {
    font-family: 'Segoe UI', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    line-height: 1.6;
}

/* الشريط العلوي المحسن */
.navbar {
    background: var(--gradient-primary) !important;
    box-shadow: var(--shadow-medium);
    padding: 1rem 0;
    border: none;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background: rgba(255,255,255,0.2);
    color: white !important;
    transform: translateY(-2px);
}

/* البطاقات المحسنة */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1.5rem;
    font-weight: bold;
    font-size: 1.1rem;
}

.card-body {
    padding: 2rem;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.stats-card.clients {
    border-left-color: var(--info-color);
}

.stats-card.cases {
    border-left-color: var(--success-color);
}

.stats-card.appointments {
    border-left-color: var(--warning-color);
}

.stats-card.invoices {
    border-left-color: var(--danger-color);
}

.stats-card .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stats-card .number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
    color: var(--dark-color);
}

.stats-card .label {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 1rem;
}

/* الأزرار المحسنة */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-success {
    background: var(--gradient-success);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* الجداول المحسنة */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table thead th {
    background: var(--gradient-primary);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
    text-align: center;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #eee;
}

/* النماذج المحسنة */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* البحث المحسن */
.search-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    margin-bottom: 2rem;
}

.search-container .card-header {
    background: var(--gradient-success);
    margin: -2rem -2rem 2rem -2rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* التنبيهات المحسنة */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-light);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* الشارات المحسنة */
.badge {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.9rem;
}

/* الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* التخطيط المتجاوب */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
}

/* تحسينات إضافية */
.container {
    max-width: 1200px;
}

.container-fluid {
    padding-left: 20px;
    padding-right: 20px;
}

/* تحسين المسافات */
.row {
    margin-left: -10px;
    margin-right: -10px;
}

.row > * {
    padding-left: 10px;
    padding-right: 10px;
}

.page-title {
    color: var(--dark-color);
    font-weight: bold;
    margin-bottom: 2rem;
    text-align: center;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.section-divider {
    height: 2px;
    background: var(--gradient-primary);
    margin: 3rem 0;
    border-radius: 1px;
}

/* تأثيرات الهوفر للأيقونات */
.icon-hover {
    transition: var(--transition);
}

.icon-hover:hover {
    transform: scale(1.2) rotate(5deg);
    color: var(--secondary-color);
}

/* تحسين التخطيط العام */
body {
    margin: 0;
    padding: 0;
}

.main-content {
    width: 100%;
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

/* إزالة المسافات الجانبية غير المرغوب فيها */
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* تحسين بطاقات الإحصائيات */
.stats-card {
    margin-bottom: 20px;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.stats-card .number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 10px 0;
}

.stats-card .label {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 15px;
}

.stats-card .icon {
    font-size: 2rem;
    margin-bottom: 15px;
}

/* تحسين المساحات */
.mt-custom {
    margin-top: 2rem;
}

.mb-custom {
    margin-bottom: 2rem;
}

.py-custom {
    padding: 2rem 0;
}
